#!/usr/bin/env python3
"""
API处理脚本：
1. 从CSV文件读取API列表
2. 对每个API调用接口1，查找"sens_type": "单车编号信息"的第一个reqid
3. 使用reqid调用接口2，统计"sens_details"列表的字符数量
"""

import csv
import requests
import urllib.parse
import json
import sys
from typing import Optional, Dict, Any

class APIProcessor:
    def __init__(self):
        # 通用的请求头
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'cookie': 'sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22ba9b7a4e5e48ce4b2dcb8434ccfd0254%22%2C%22first_id%22%3A%2218457014584980-00e28086d9054478-18525635-3686400-18457014585a45%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTg0NTcwMTQ1ODQ5ODAtMDBlMjgwODZkOTA1NDQ3OC0xODUyNTYzNS0zNjg2NDAwLTE4NDU3MDE0NTg1YTQ1IiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiYmE5YjdhNGU1ZTQ4Y2U0YjJkY2I4NDM0Y2NmZDAyNTQifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22ba9b7a4e5e48ce4b2dcb8434ccfd0254%22%7D%2C%22%24device_id%22%3A%2218457014584980-00e28086d9054478-18525635-3686400-18457014585a45%22%7D; _hjSessionUser_2777326=eyJpZCI6Ijc2Y2FjZDhmLTU2MjEtNWQ2Ny05OTg5LWExOTBhMjgwNmE2NyIsImNyZWF0ZWQiOjE2NzM4NzI2MTYwMTgsImV4aXN0aW5nIjp0cnVlfQ==; ubt_ssid=7sybshb0xsedsfldqihwwv4uvhlkj1o2_2024-07-11; sso_token=bearer_635cb714-e62b-471f-86bd-f476aeeda3c9; btdp-sess=709f4384-a57d-456d-9778-1ddfb7dc42d3; btdp-sess.sig=GF-k4glw5so_LbreA2obnL-l38g',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
        
        # 接口1的基础URL
        self.api1_base_url = 'https://yazx.hellobike.cn/api/api/assets/sens-sample/info'
        
        # 接口2的基础URL
        self.api2_base_url = 'https://yazx.hellobike.cn/api/api/assets/sens-sample/detail'

    def read_apis_from_csv(self, csv_file: str) -> list:
        """从CSV文件读取API列表"""
        apis = []
        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                reader = csv.reader(file)
                for row in reader:
                    if row:  # 跳过空行
                        apis.append(row[0].strip())
        except Exception as e:
            print(f"读取CSV文件失败: {e}")
            return []
        
        print(f"从CSV文件读取到 {len(apis)} 个API")
        return apis

    def call_api1(self, api: str) -> Optional[Dict[Any, Any]]:
        """调用接口1获取sens-sample信息"""
        try:
            # URL编码API
            encoded_api = urllib.parse.quote(api, safe='')
            
            # 构建完整URL
            url = f"{self.api1_base_url}?api={encoded_api}&method=POST"
            
            print(f"调用接口1: {api}")
            print(f"请求URL: {url}")
            
            response = requests.get(url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"接口1调用失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"调用接口1异常: {e}")
            return None

    def find_bike_info_reqid(self, response_data: Dict[Any, Any]) -> Optional[str]:
        """从接口1响应中查找"sens_type": "单车编号信息"的第一个reqid"""
        try:
            if 'data' not in response_data:
                return None

            data = response_data['data']
            if not isinstance(data, dict):
                return None

            # 检查rspData中的单车编号信息
            if 'rspData' in data:
                rsp_data = data['rspData']
                if isinstance(rsp_data, list):
                    for rsp_item in rsp_data:
                        if isinstance(rsp_item, dict) and rsp_item.get('sens_type') == '单车编号信息':
                            print(f"找到单车编号信息: {rsp_item}")
                            # 获取data中的第一个reqid
                            if 'data' in rsp_item and isinstance(rsp_item['data'], list):
                                for data_item in rsp_item['data']:
                                    if isinstance(data_item, dict) and 'data' in data_item:
                                        if isinstance(data_item['data'], list) and len(data_item['data']) > 0:
                                            first_item = data_item['data'][0]
                                            if isinstance(first_item, dict) and 'reqid' in first_item:
                                                reqid = first_item['reqid']
                                                print(f"找到第一个reqid: {reqid}")
                                                return reqid

            # 如果rspData中没有找到，检查reqData
            if 'reqData' in data:
                req_data = data['reqData']
                if isinstance(req_data, list):
                    for req_item in req_data:
                        if isinstance(req_item, dict) and req_item.get('sens_type') == '单车编号信息':
                            print(f"在reqData中找到单车编号信息: {req_item}")
                            # 获取data中的第一个reqid
                            if 'data' in req_item and isinstance(req_item['data'], list):
                                for data_item in req_item['data']:
                                    if isinstance(data_item, dict) and 'data' in data_item:
                                        if isinstance(data_item['data'], list) and len(data_item['data']) > 0:
                                            first_item = data_item['data'][0]
                                            if isinstance(first_item, dict) and 'reqid' in first_item:
                                                reqid = first_item['reqid']
                                                print(f"找到第一个reqid: {reqid}")
                                                return reqid

            return None

        except Exception as e:
            print(f"解析接口1响应异常: {e}")
            return None

    def call_api2(self, reqid: str) -> Optional[Dict[Any, Any]]:
        """调用接口2获取详细信息"""
        try:
            url = f"{self.api2_base_url}?reqid={reqid}&node_tag=ana_4&decode=true"
            
            print(f"调用接口2，reqid: {reqid}")
            print(f"请求URL: {url}")
            
            response = requests.get(url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"接口2调用失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"调用接口2异常: {e}")
            return None

    def count_sens_details_chars(self, response_data: Dict[Any, Any]) -> int:
        """统计sens_details列表中的字符数量"""
        try:
            # 检查data字段中的sens_details
            if 'data' in response_data and isinstance(response_data['data'], dict):
                data = response_data['data']
                if 'sens_details' in data:
                    sens_details = data['sens_details']
                    if isinstance(sens_details, list):
                        total_chars = 0
                        for item in sens_details:
                            if isinstance(item, str):
                                total_chars += len(item)
                            elif isinstance(item, dict):
                                # 如果是字典，将其转换为JSON字符串计算长度
                                total_chars += len(json.dumps(item, ensure_ascii=False))
                            else:
                                # 其他类型转换为字符串
                                total_chars += len(str(item))

                        print(f"sens_details列表包含 {len(sens_details)} 个项目，总字符数: {total_chars}")
                        return total_chars
                    else:
                        print("sens_details不是列表类型")
                        return 0
                else:
                    print("data中没有sens_details字段")
                    return 0
            else:
                print("响应中没有data字段或data不是字典类型")
                return 0

        except Exception as e:
            print(f"统计字符数异常: {e}")
            return 0

    def process_single_api(self, api: str) -> Optional[int]:
        """处理单个API的完整流程"""
        print(f"\n{'='*60}")
        print(f"处理API: {api}")
        print(f"{'='*60}")
        
        # 步骤1: 调用接口1
        api1_response = self.call_api1(api)
        if not api1_response:
            print("接口1调用失败，跳过此API")
            return None
            
        print(f"接口1响应: {json.dumps(api1_response, ensure_ascii=False, indent=2)}")
        
        # 步骤2: 查找reqid
        reqid = self.find_bike_info_reqid(api1_response)
        if not reqid:
            print("未找到单车编号信息的reqid，跳过此API")
            return None
            
        print(f"找到reqid: {reqid}")
        
        # 步骤3: 调用接口2
        api2_response = self.call_api2(reqid)
        if not api2_response:
            print("接口2调用失败")
            return None
            
        print(f"接口2响应: {json.dumps(api2_response, ensure_ascii=False, indent=2)}")
        
        # 步骤4: 统计字符数
        char_count = self.count_sens_details_chars(api2_response)
        print(f"sens_details字符数统计结果: {char_count}")
        
        return char_count

    def process_all_apis(self, csv_file: str):
        """处理所有API"""
        apis = self.read_apis_from_csv(csv_file)
        if not apis:
            print("没有读取到API列表")
            return
            
        results = []
        
        for i, api in enumerate(apis, 1):
            print(f"\n处理进度: {i}/{len(apis)}")
            char_count = self.process_single_api(api)
            
            result = {
                'api': api,
                'char_count': char_count,
                'success': char_count is not None
            }
            results.append(result)
            
            # 如果成功处理了一个API，可以选择是否继续处理其他API
            if char_count is not None:
                print(f"✅ API处理成功: {api}, 字符数: {char_count}")
                # 可以在这里添加break来只处理第一个成功的API
                # break
            else:
                print(f"❌ API处理失败: {api}")

            # 只处理前10个API进行测试
            # if i >= 10:
            #     print("已处理前10个API，停止处理")
            #     break
        
        # 输出总结
        print(f"\n{'='*60}")
        print("处理结果总结:")
        print(f"{'='*60}")
        
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        print(f"总共处理API数量: {len(results)}")
        print(f"成功处理数量: {len(successful_results)}")
        print(f"失败处理数量: {len(failed_results)}")
        
        if successful_results:
            print("\n成功处理的API:")
            for result in successful_results:
                print(f"  - {result['api']}: {result['char_count']} 字符")
        
        if failed_results:
            print("\n失败处理的API:")
            for result in failed_results:
                print(f"  - {result['api']}")

def main():
    if len(sys.argv) != 2:
        print("使用方法: python api_processor.py <csv_file>")
        print("示例: python api_processor.py api-actions.csv")
        sys.exit(1)
    
    csv_file = sys.argv[1]
    processor = APIProcessor()
    processor.process_all_apis(csv_file)

if __name__ == "__main__":
    main()
