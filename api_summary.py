#!/usr/bin/env python3
"""
API处理总结脚本：
演示如何从CSV文件读取API，调用接口1和接口2，统计sens_details字符数量
"""

import csv
import requests
import urllib.parse
import json

def main():
    # 示例：处理第一个成功的API
    csv_file = "api-actions.csv"
    
    # 通用的请求头
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'cookie': 'sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22ba9b7a4e5e48ce4b2dcb8434ccfd0254%22%2C%22first_id%22%3A%2218457014584980-00e28086d9054478-18525635-3686400-18457014585a45%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTg0NTcwMTQ1ODQ5ODAtMDBlMjgwODZkOTA1NDQ3OC0xODUyNTYzNS0zNjg2NDAwLTE4NDU3MDE0NTg1YTQ1IiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiYmE5YjdhNGU1ZTQ4Y2U0YjJkY2I4NDM0Y2NmZDAyNTQifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22ba9b7a4e5e48ce4b2dcb8434ccfd0254%22%7D%2C%22%24device_id%22%3A%2218457014584980-00e28086d9054478-18525635-3686400-18457014585a45%22%7D; _hjSessionUser_2777326=eyJpZCI6Ijc2Y2FjZDhmLTU2MjEtNWQ2Ny05OTg5LWExOTBhMjgwNmE2NyIsImNyZWF0ZWQiOjE2NzM4NzI2MTYwMTgsImV4aXN0aW5nIjp0cnVlfQ==; ubt_ssid=7sybshb0xsedsfldqihwwv4uvhlkj1o2_2024-07-11; sso_token=bearer_635cb714-e62b-471f-86bd-f476aeeda3c9; btdp-sess=709f4384-a57d-456d-9778-1ddfb7dc42d3; btdp-sess.sig=GF-k4glw5so_LbreA2obnL-l38g',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    # 读取CSV文件中的第一个API
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        first_api = next(reader)[0].strip()
    
    print("=" * 80)
    print("API处理演示结果")
    print("=" * 80)
    print(f"处理的API: {first_api}")
    
    # 步骤1: 调用接口1
    encoded_api = urllib.parse.quote(first_api, safe='')
    api1_url = f"https://yazx.hellobike.cn/api/api/assets/sens-sample/info?api={encoded_api}&method=POST"
    
    print(f"\n步骤1: 调用接口1")
    print(f"URL: {api1_url}")
    
    response1 = requests.get(api1_url, headers=headers, timeout=30)
    if response1.status_code != 200:
        print(f"接口1调用失败，状态码: {response1.status_code}")
        return
    
    data1 = response1.json()
    print(f"接口1响应状态: 成功")
    
    # 步骤2: 查找reqid
    print(f"\n步骤2: 查找'sens_type': '单车编号信息'的第一个reqid")
    
    reqid = None
    if 'data' in data1 and 'rspData' in data1['data']:
        for rsp_item in data1['data']['rspData']:
            if rsp_item.get('sens_type') == '单车编号信息':
                for data_item in rsp_item['data']:
                    if 'data' in data_item and len(data_item['data']) > 0:
                        reqid = data_item['data'][0]['reqid']
                        break
                if reqid:
                    break
    
    if not reqid:
        print("未找到单车编号信息的reqid")
        return
    
    print(f"找到reqid: {reqid}")
    
    # 步骤3: 调用接口2
    print(f"\n步骤3: 调用接口2")
    api2_url = f"https://yazx.hellobike.cn/api/api/assets/sens-sample/detail?reqid={reqid}&node_tag=ana_4&decode=true"
    print(f"URL: {api2_url}")
    
    response2 = requests.get(api2_url, headers=headers, timeout=30)
    if response2.status_code != 200:
        print(f"接口2调用失败，状态码: {response2.status_code}")
        return
    
    data2 = response2.json()
    print(f"接口2响应状态: 成功")
    
    # 步骤4: 统计字符数
    print(f"\n步骤4: 统计sens_details列表中的字符数量")
    
    if 'data' in data2 and 'sens_details' in data2['data']:
        sens_details = data2['data']['sens_details']
        total_chars = sum(len(str(item)) for item in sens_details)
        
        print(f"sens_details内容: {sens_details}")
        print(f"列表项目数量: {len(sens_details)}")
        print(f"总字符数: {total_chars}")
        
        print("\n" + "=" * 80)
        print("最终结果")
        print("=" * 80)
        print(f"API: {first_api}")
        print(f"reqid: {reqid}")
        print(f"sens_details字符数量: {total_chars}")
        print("=" * 80)
        
    else:
        print("响应中没有找到sens_details字段")

if __name__ == "__main__":
    main()
